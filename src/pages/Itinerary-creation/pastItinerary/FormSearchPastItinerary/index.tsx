import BasicButton from '@/components/Commons/BasicButton';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import BasicInput from '@/components/Commons/BasicInput';
import CustomSelectPrefectures from '@/components/Commons/CustomSelectPrefectures';
import SelectTouristSpot from '@/components/Commons/SelectTouristSpot';
import SearchSVG from '@/components/SVG/SearchSVGWhite';
import type { FormInstance } from 'antd';
import { Checkbox, Form, Image } from 'antd';
import type { FormProps } from 'antd/es/form';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import ArrowRight from '@/assets/imgs/svg/arrow-right-02.svg';
import moment from 'moment';
import SelectLocalAgent from '@/components/Commons/SelectLocalAgent';
import { TEXT_TITLE } from '@/constants/commonText';

type PastItineraryFormProps = {
  onSubmit?: (v: any) => void;
  // onResetField?: () => void;
};

export type PastItineraryFormRefType = {
  form: FormInstance<any>;
};

const FormSearchPastItinerary = forwardRef<PastItineraryFormRefType, PastItineraryFormProps>(
  ({ onSubmit, ...others }: PastItineraryFormProps & FormProps, ref) => {
    const [form] = Form.useForm();
    const [keyword, setKeyword] = useState<string>('');

    const startDate = Form.useWatch('day_go_start', form);
    const endDate = Form.useWatch('day_go_end', form);

    useEffect(() => {
      if (moment(endDate).isBefore(startDate)) {
        form.setFieldValue('day_go_start', endDate);
      }
    }, [endDate]);

    useEffect(() => {
      if (endDate && moment(startDate).isAfter(endDate)) {
        form.setFieldValue('day_go_end', startDate);
      }
    }, [startDate]);

    useImperativeHandle(ref, () => ({
      form,
    }));

    return (
      <Form {...others} form={form} onFinish={onSubmit}>
        <div className="flex items-center gap-x-[16px] gap-y-[8px] flex-wrap">
          {/* <Form.Item>
            <BasicInput title="キーワード" placeholder="キーワードで検索する" className="h-9" />
          </Form.Item> */}

          <div className="[&_.ant-select-selector]:!h-9 [&_.ant-select-selector]:!w-[140px]">
            <Form.Item name={'city'}>
              <CustomSelectPrefectures
                hasMunicipalities={false}
                placeHolder={{
                  placeHolderPrefecture: 'すべて',
                  placeHolderMunicipalities: 'すべて',
                }}
              />
            </Form.Item>
          </div>

          <Form.Item name={'touristSpot'}>
            <SelectTouristSpot title="観光地" allowClear />
          </Form.Item>
          {/* <div className="[&_.ant-select-selector]:!h-9 [&_.ant-select-selector]:!w-[140px]">
            <Form.Item name={'month'}>
              <BasicSelect options={MONTH} title="時期" placeholder="すべて" allowClear />
            </Form.Item>
          </div> */}

          <div className="col-span-4 flex gap-1 items-center">
            <Form.Item className="flex-1 !w-[140px]" name="day_go_start">
              <BasicDatePicker title="出発日" className="!h-9" placeholder="すべて" />
            </Form.Item>
            <div className="">
              {/* <div className="h-2" /> */}
              <div className="flex items-center">
                <Image preview={false} src={ArrowRight} alt="arrow right" />
              </div>
            </div>

            <Form.Item className="flex-1 !w-[140px]" name="day_go_end">
              <BasicDatePicker title="帰着日" className="!h-9" placeholder="すべて" />
            </Form.Item>
          </div>

          <div className="w-[140px]">
            <Form.Item className="" name="business_partner_id">
              <SelectLocalAgent allowClear title={TEXT_TITLE.Customer} className="!w-full" />
            </Form.Item>
          </div>

          <div className="w-[140px]">
            <Form.Item name="keyword">
              <BasicInput
                value={keyword}
                title="ツアー名または旅行ID"
                placeholder="キーワードで検索する"
                onChange={(e) => setKeyword(e.target.value)}
                allowClear
                className="!h-9 max-w-[300px] !rounded-[0.5rem]"
              />
            </Form.Item>
          </div>

          <div className="w-[140px]">
            <Form.Item name={'is_model_course'} valuePropName="checked">
              {/* <div className="mb-2 text-[13px] leading-4 font-medium" /> */}
              {/* <Checkbox>モデルコース</Checkbox> */}
              {/* <BasicInput
                value={keyword}
                title="ツアー名または旅行ID"
                placeholder="キーワードで検索する"
                onChange={(e) => setKeyword(e.target.value)}
                allowClear
                className="!h-9 max-w-[300px] !rounded-[0.5rem]"
              /> */}
              <div className="flex flex-col">
                <div className="mb-2 text-[13px] leading-4 font-medium">&nbsp;</div>
                <Checkbox>モデルコース</Checkbox>
              </div>
            </Form.Item>
          </div>
          <div className="mb-6 flex flex-col">
            <div className="mb-2 text-[13px] leading-4 font-medium">&nbsp;</div>
            <div className="flex items-center space-x-[6px]">
              <BasicButton
                onClick={() => form.submit()}
                styleType="accept"
                className="flex items-center justify-center border-main-color w-[120px] h-9 space-x-[8px]"
              >
                <SearchSVG />
                検索
              </BasicButton>
              {/* <BasicButton
              onClick={() => {
                form.resetFields();
                onSubmit({
                  prefecture_code: undefined,
                  travel_spot_id: undefined,
                  month: undefined,
                  model_course: undefined,
                });
              }}
              styleType="back"
              className="flex items-center justify-center border-main-color w-[120px] h-9 space-x-[8px]"
            >
              リセット
            </BasicButton> */}
            </div>
          </div>
        </div>
      </Form>
    );
  },
);

export default FormSearchPastItinerary;
