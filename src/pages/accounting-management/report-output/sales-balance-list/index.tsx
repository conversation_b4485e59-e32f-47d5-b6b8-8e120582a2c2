import type {
  ResponseReportSaleTypeByBP,
  ResponseReportSaleTypeBySubject,
  ResponseReportSaleTypeByTravel,
} from '@/apis/reportOutput/sale';
import { getReportSale } from '@/apis/reportOutput/sale';
import BasicButton from '@/components/Commons/BasicButton';
import PageContainer from '@/components/Commons/Page/Container';
import { TEXT_ACTION } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { formatMoney, formatPercent } from '@/utils';
import { DownloadOutlined } from '@ant-design/icons';
import { Form } from 'antd';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { useUrlSearchParams } from 'use-url-search-params';
import HeaderAction from './components/HeaderAction';
import TableByCustomerID from './components/TableByCustomerID';
import TableBySummarySubject from './components/TableBySummarySubject';
import TableByTripID from './components/TableByTripID';
import { openNotificationFail } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';

const initSearchParams = {};

const InvoicesPage = () => {
  const [formHeader] = Form.useForm();
  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);
  const [isLoading, setIsLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [SalesAmount, setSalesAmount] = useState(0);
  const [PurchaseAmount, setPurchaseAmount] = useState(0);
  const [GrossProfitAmount, setGrossProfitAmount] = useState(0);
  const [RAmount, setRAmount] = useState(0);
  const [GrossProfitTotal, setGrossProfitTotal] = useState(0);
  const [GrossProfitRate, setGrossProfitRate] = useState(0);
  const typeDate = parameter.type_date;

  const formatDataTypeTravel = (data: ResponseReportSaleTypeByTravel[]) => {
    let totalSalesAmount = 0;
    let totalPurchaseAmount = 0;
    let totalGrossProfitAmount = 0;
    let totalRAmount = 0;
    let totalGrossProfitTotal = 0;
    const dataRender: any[] = [];
    let key = 1;

    const dataSortByTravel = [...data].sort((a, b) => {
      return Number(a.travel_code) - Number(b.travel_code);
    });

    dataSortByTravel.forEach((itemData) => {
      let Sales_amount = 0;
      let Purchase_amount = 0;
      let Gross_profit_amount = 0;
      let R_amount = 0;
      let Gross_profit_total = 0;
      itemData.business_partner_items.forEach((itemBP) => {
        dataRender.push({
          index: key,
          key: key,
          departure_date: itemData.departure_date,
          return_date: itemData.return_date,
          travel_code: itemData?.travel_code,
          tour_name: itemData.tour_name,
          sale_date: itemBP?.sale_date,
          business_partner_name: itemBP?.business_partner_name,
          business_partner_code: itemBP?.business_partner_code,
          Sales_amount: formatMoney(itemBP?.total_sale_amount || 0),
          Purchase_amount: formatMoney(itemBP?.total_purchase_amount || 0),
          Gross_profit_amount: formatMoney(itemBP?.gross_profit_amount || 0),
          R_amount: formatMoney(itemBP?.commission || 0),
          Gross_profit_total: formatMoney(itemBP?.total_gross_profit || 0),
          Gross_profit_rate: itemBP?.gross_profit_rate || 0,
          voucher_posting_date: itemBP?.voucher_posting_date,
          Salesperson: itemData?.admin?.name,
        });
        key++;
      });
      Sales_amount += itemData.total_sale_amount;
      Purchase_amount += itemData.total_purchase_amount;
      Gross_profit_amount += itemData.total_gross_profit_amount;
      R_amount += itemData.total_commission;
      Gross_profit_total += itemData.total_gross_profit;
      const Gross_profit_rate = Gross_profit_total / Sales_amount;
      if (itemData.business_partner_items.length > 1) {
        dataRender.push({
          index: 'total' + key,
          type: 'total',
          title: `旅行ID:${itemData.travel_code} 合計`,
          Sales_amount: formatMoney(Sales_amount),
          Purchase_amount: formatMoney(Purchase_amount),
          Gross_profit_amount: formatMoney(Gross_profit_amount),
          R_amount: formatMoney(R_amount),
          Gross_profit_total: formatMoney(Gross_profit_total),
          Gross_profit_rate: formatPercent(Gross_profit_rate * 100, 2, '%'),
        });
      }

      totalSalesAmount += Sales_amount;
      totalPurchaseAmount += Purchase_amount;
      totalGrossProfitAmount += Gross_profit_amount;
      totalRAmount += R_amount;
      totalGrossProfitTotal += Gross_profit_total;
    });
    setSalesAmount(totalSalesAmount);
    setPurchaseAmount(totalPurchaseAmount);
    setGrossProfitAmount(totalGrossProfitAmount);
    setRAmount(totalRAmount);
    setGrossProfitTotal(totalGrossProfitTotal);
    setGrossProfitRate((totalGrossProfitTotal / totalSalesAmount) * 100);
    return dataRender;
  };

  const formatDataTypeBusinessPartner = (data: ResponseReportSaleTypeByBP[]) => {
    let Sales_amount = 0;
    let Purchase_amount = 0;
    let Gross_profit_amount = 0;
    let R_amount = 0;
    let Gross_profit_total = 0;

    const dataRender: any[] = [];
    let key = 1;
    data.forEach((groupData, index) => {
      groupData?.travels?.forEach((itemDetail, indexDetail) => {
        if (parameter.type_date === 'voucher_posting_date') {
          Object.keys(itemDetail.sales_by_months).forEach((keyMonth, indexGroup) => {
            const itemGroupByMonth = itemDetail.sales_by_months[keyMonth];
            dataRender.push({
              key: indexGroup == 0 && indexDetail == 0 && key,
              index: `itemGroupByMonth_${indexDetail}_${key}_${index}_${indexGroup}`,
              departure_date: itemDetail.departure_date,
              travel_code: itemDetail?.travel_code,
              tour_name: itemDetail.tour_name,
              business_partner_name: groupData?.business_partner_name,
              business_partner_code: groupData?.business_partner_code,
              Salesperson: indexDetail == 0 && itemDetail?.admin?.name,
              Sales_amount: formatMoney(itemGroupByMonth.total_sale_amount),
              month: keyMonth,
              Purchase_amount: formatMoney(itemGroupByMonth.total_purchase_amount),
              Gross_profit_amount: formatMoney(itemGroupByMonth.gross_profit_amount),
              R_amount: formatMoney(itemGroupByMonth.commission),
              Gross_profit_total: formatMoney(itemGroupByMonth.total_gross_profit),
              Gross_profit_rate: formatPercent(itemGroupByMonth.gross_profit_rate, 2, '%'),
            });
          });
          if (Object.keys(itemDetail.sales_by_months).length > 1) {
            dataRender.push({
              index: `itemGroupByMonth_total_travel_${indexDetail}_${key}_${index}`,
              title: `旅行ID:${itemDetail.travel_code} 合計`,
              type: 'total_travel',
              Sales_amount: formatMoney(itemDetail?.total_sales_amount),
              Purchase_amount: formatMoney(itemDetail?.total_purchase_amount ?? 0),
              Gross_profit_amount: formatMoney(itemDetail?.gross_profit),
              R_amount: formatMoney(itemDetail?.commission),
              Gross_profit_total: formatMoney(itemDetail?.total_gross_profit),
              Gross_profit_rate: itemDetail?.gross_profit_rate,
            });
          }
        } else {
          dataRender.push({
            index: key + index,
            key: key,
            departure_date: itemDetail.departure_date,
            return_date: itemDetail.return_date,
            travel_code: itemDetail?.travel_code,
            tour_name: itemDetail.tour_name,
            business_partner_name: indexDetail === 0 && groupData?.business_partner_name,
            business_partner_code: indexDetail === 0 && groupData?.business_partner_code,
            Salesperson: itemDetail?.admin?.name,
            Accounting_date: itemDetail.voucher_posting_date,
            Sales_amount: formatMoney(itemDetail?.total_sales_amount),
            Purchase_amount: formatMoney(itemDetail?.total_purchase_amount ?? 0),
            Gross_profit_amount: formatMoney(itemDetail?.gross_profit),
            R_amount: formatMoney(itemDetail?.commission),
            Gross_profit_total: formatMoney(itemDetail?.total_gross_profit),
            Gross_profit_rate: formatPercent(itemDetail?.gross_profit_rate, 2, '%'),
          });
        }

        key++;
      });
      const groupSalesAmount = groupData?.travels.reduce(
        (acc, item) => acc + item.total_sales_amount,
        0,
      );
      const groupPurchaseAmount = groupData?.total_purchase_amount;
      const groupGrossProfitAmount = groupData?.total_gross_profit_amount;
      const groupRAmount = groupData?.total_commission;
      const groupGrossProfitTotal = groupData?.total_gross_profit;

      Sales_amount += groupSalesAmount;
      Purchase_amount += groupPurchaseAmount;
      Gross_profit_amount += groupGrossProfitAmount;
      R_amount += groupRAmount;
      Gross_profit_total += groupGrossProfitTotal;
      if (groupData?.travels.length > 1) {
        dataRender.push({
          index: key + index,
          type: 'total',
          business_partner_name: groupData?.business_partner_name,
          business_partner_code: groupData?.business_partner_code,
          Sales_amount: formatMoney(groupSalesAmount),
          Purchase_amount: formatMoney(groupPurchaseAmount),
          Gross_profit_amount: formatMoney(groupGrossProfitAmount),
          R_amount: formatMoney(groupRAmount),
          Gross_profit_total: formatMoney(groupGrossProfitTotal),
          Gross_profit_rate:
            groupGrossProfitTotal &&
            groupSalesAmount &&
            formatPercent((groupGrossProfitTotal / groupSalesAmount) * 100),
        });
      }
    });
    setSalesAmount(Sales_amount);
    setPurchaseAmount(Purchase_amount);
    setGrossProfitAmount(Gross_profit_amount);
    setRAmount(R_amount);
    setGrossProfitTotal(Gross_profit_total);
    setGrossProfitRate((Gross_profit_total / Sales_amount) * 100);
    return dataRender;
  };

  const formatDataTypeSubject = (data: ResponseReportSaleTypeBySubject[]) => {
    const Sales_amount = data.reduce((acc, item) => acc + item.total_sale_amount, 0);
    const Purchase_amount = data.reduce((acc, item) => acc + item.total_purchase_amount, 0);
    const Gross_profit_amount = data.reduce((acc, item) => acc + item.total_gross_profit_amount, 0);
    const R_amount = data.reduce((acc, item) => acc + item?.total_commission, 0);
    const Gross_profit_total = data.reduce((acc, item) => acc + item.total_gross_profit, 0);
    setSalesAmount(Sales_amount);
    setPurchaseAmount(Purchase_amount);
    setGrossProfitAmount(Gross_profit_amount);
    setRAmount(R_amount);
    setGrossProfitTotal(Gross_profit_total);
    setGrossProfitRate((Gross_profit_total / Sales_amount) * 100);
    const dataRender: any[] = [];
    let key = 1;

    data.forEach((itemDetail, index) => {
      itemDetail.subject_items.forEach((itemSubject, indexSubject) => {
        dataRender.push({
          index: key + index,
          key: indexSubject == 0 && key,
          travel_code: indexSubject == 0 && itemDetail?.travel_code,
          tour_name: indexSubject == 0 && itemDetail.tour_name,
          summary_subject_name: itemSubject?.summary_subject_name,
          Sales_amount: formatMoney(itemSubject?.total_sale_amount),
          Purchase_amount: formatMoney(itemSubject?.total_purchase_amount),
          Gross_profit_amount: formatMoney(itemSubject?.gross_profit_amount),
          R_amount: formatMoney(itemSubject?.commission),
          Gross_profit_total: formatMoney(itemSubject?.total_gross_profit),
          Gross_profit_rate: itemSubject?.gross_profit_rate,
        });
      });
      key++;
      if (itemDetail.subject_items.length > 1) {
        dataRender.push({
          index: key + index,
          type: 'total',
          title: `旅行ID:${itemDetail.travel_code} 合計`,
          Sales_amount: formatMoney(itemDetail.total_sale_amount),
          Purchase_amount: formatMoney(itemDetail.total_purchase_amount),
          Gross_profit_amount: formatMoney(itemDetail.total_gross_profit_amount),
          R_amount: formatMoney(itemDetail.total_commission),
          Gross_profit_total: formatMoney(itemDetail.total_gross_profit),
          Gross_profit_rate: itemDetail.total_gross_profit_rate,
        });
      }
    });
    return dataRender;
  };

  const onFetchData = async (isExport?: boolean) => {
    setIsLoading(true);
    try {
      const reportType = parameter.report_type;
      if (!reportType) {
        setIsLoading(false);
        return;
      }
      let travelTypeListParam = undefined;
      try {
        if (parameter?.travel_type && Array.isArray(parameter.travel_type)) {
          travelTypeListParam = parameter?.travel_type?.map((item: string) => Number(item));
        } else if (parameter?.travel_type && typeof parameter.travel_type === 'string') {
          travelTypeListParam = [Number(parameter?.travel_type)];
        }
      } catch (error) {
        console.log('travelTypeListParam error', error);
      }
      const valueSearch: { [key: string]: any } = {
        start: parameter.startDate
          ? moment(parameter.startDate as string).format('YYYY-MM-DD')
          : moment(parameter?.endDate as string)
              .subtract(11, 'months')
              .startOf('month')
              .format('YYYY-MM-DD'),
        finish: parameter.endDate
          ? moment(parameter.endDate as string).format('YYYY-MM-DD')
          : undefined,
        limit: parameter.limit ? (parameter.limit as string) : 'all',
        report_type: reportType as string,
        type_date: parameter.type_date as string,
        admin_id: parameter?.admin_id
          ? JSON.parse(parameter?.admin_id as string)?.value
          : undefined,
        business_partner_id: parameter?.business_partner_id
          ? JSON.parse(parameter?.business_partner_id as string)?.value
          : undefined,
        travel_type: travelTypeListParam,
        is_excluding_tax: parameter?.is_excluding_tax
          ? JSON.parse(parameter?.is_excluding_tax as string)
          : 1,
      };
      if (isExport) {
        valueSearch.is_export_excel = 1;
        const resExport = await getReportSale(valueSearch);
        window.open(resExport.data?.data?.file_url);
        setIsLoading(false);
        return;
      }
      setDataSource([]);
      const resGetList = await getReportSale(valueSearch);
      if (resGetList.status === STATUS_CODE.SUCCESSFUL) {
        let dataRender: any[] = [];
        if (reportType === 'report_by_travel') {
          dataRender = formatDataTypeTravel(
            resGetList.data?.data?.data as ResponseReportSaleTypeByTravel[],
          );
        } else if (reportType === 'report_by_business_partner') {
          dataRender = formatDataTypeBusinessPartner(
            resGetList.data?.data?.data as ResponseReportSaleTypeByBP[],
          );
        } else if (reportType === 'report_by_subject') {
          dataRender = formatDataTypeSubject(
            resGetList.data?.data?.data as ResponseReportSaleTypeBySubject[],
          );
        }
        setDataSource(dataRender);
      } else {
        openNotificationFail({ message: MESSAGE_ALERT.FAILED_TO_GET_DATA });
      }
    } catch (error) {
      console.log('error', error);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    // set Form Header
    if (formHeader) {
      let travelTypeListParam = undefined;
      try {
        if (parameter?.travel_type && Array.isArray(parameter.travel_type)) {
          travelTypeListParam = parameter?.travel_type?.map((item: string) => Number(item));
        } else if (parameter?.travel_type && typeof parameter.travel_type === 'string') {
          travelTypeListParam = [Number(parameter?.travel_type)];
        }
      } catch (error) {
        console.log('travelTypeListParam error', error);
      }
      formHeader.setFieldsValue({
        ...parameter,
        startDate: parameter.startDate ? moment(parameter.startDate as string) : undefined,
        endDate: parameter.endDate
          ? moment(parameter.endDate as string)
          : moment().subtract(1, 'months').endOf('month'),
        admin_id: parameter?.admin_id ? JSON.parse(parameter?.admin_id as string) : undefined,
        business_partner_id: parameter?.business_partner_id
          ? JSON.parse(parameter?.business_partner_id as string)
          : undefined,
        travel_type: travelTypeListParam,
        type_date: (parameter.type_date as string) ?? 'voucher_posting_date',
        is_excluding_tax: parameter?.is_excluding_tax
          ? JSON.parse(parameter?.is_excluding_tax as string)
          : 1,
      });
    }
    onFetchData();
  }, [parameter]);

  const onSubmit = async () => {
    const valueSubmit = await formHeader.validateFields();
    setParameter({
      ...valueSubmit,
      startDate: valueSubmit.startDate && moment(valueSubmit.startDate).format('YYYY-MM-DD'),
      endDate: valueSubmit.endDate && moment(valueSubmit.endDate).format('YYYY-MM-DD'),
    });
  };

  return (
    <PageContainer>
      <HeaderAction form={formHeader} onSubmit={onSubmit} />
      <div className="p-2 rounded-xl bg-white mt-6">
        {(!parameter.report_type || parameter.report_type == 'report_by_travel') && (
          <TableByTripID
            isLoading={isLoading}
            dataSource={dataSource}
            SalesAmount={SalesAmount}
            PurchaseAmount={PurchaseAmount}
            GrossProfitAmount={GrossProfitAmount}
            RAmount={RAmount}
            GrossProfitTotal={GrossProfitTotal}
            GrossProfitRate={GrossProfitRate}
            typeDate={typeDate as string}
          />
        )}
        {parameter.report_type == 'report_by_business_partner' && (
          <TableByCustomerID
            isLoading={isLoading}
            dataSource={dataSource}
            SalesAmount={SalesAmount}
            PurchaseAmount={PurchaseAmount}
            GrossProfitAmount={GrossProfitAmount}
            RAmount={RAmount}
            GrossProfitTotal={GrossProfitTotal}
            GrossProfitRate={GrossProfitRate}
            typeDate={typeDate as string}
          />
        )}
        {parameter.report_type == 'report_by_subject' && (
          <TableBySummarySubject
            isLoading={isLoading}
            dataSource={dataSource}
            SalesAmount={SalesAmount}
            PurchaseAmount={PurchaseAmount}
            GrossProfitAmount={GrossProfitAmount}
            RAmount={RAmount}
            GrossProfitTotal={GrossProfitTotal}
            GrossProfitRate={GrossProfitRate}
          />
        )}
      </div>

      <div className="flex gap-6 justify-center mt-10">
        <BasicButton
          disabled={isLoading}
          styleType="accept"
          className={`!w-[190px] flex justify-center items-center ${
            isLoading ? '!bg-[gray] hover:!bg-[gray] !text-white' : ''
          }`}
          onClick={() => {
            onFetchData(true);
          }}
        >
          <DownloadOutlined style={{ color: 'white' }} />
          <p>{TEXT_ACTION.Report_output}</p>
        </BasicButton>
      </div>
    </PageContainer>
  );
};

export default InvoicesPage;
